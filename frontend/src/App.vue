<template>
  <div id="app">
    <div class="app-tabs">
      <button
        @click="currentTab = 'chat'"
        :class="{ active: currentTab === 'chat' }"
        class="tab-btn"
      >
        聊天界面
      </button>
      <button
        @click="currentTab = 'test'"
        :class="{ active: currentTab === 'test' }"
        class="tab-btn"
      >
        API测试
      </button>
    </div>

    <ChatInterface v-if="currentTab === 'chat'" />
    <ApiTest v-if="currentTab === 'test'" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ChatInterface from '@/components/ChatInterface.vue';
import ApiTest from '@/test/ApiTest.vue';

const currentTab = ref('test'); // 默认显示测试页面
</script>

<style>
#app {
  height: 100vh;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

.app-tabs {
  display: flex;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.tab-btn {
  padding: 12px 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  font-size: 14px;
}

.tab-btn.active {
  background: white;
  border-bottom-color: #007bff;
  color: #007bff;
}

.tab-btn:hover {
  background: #e9ecef;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}
</style>
