<template>
  <div class="api-test">
    <h2>API测试页面</h2>
    
    <div class="test-section">
      <h3>健康检查</h3>
      <button @click="testHealth" :disabled="loading">测试健康检查</button>
      <div v-if="healthResult" class="result">
        <pre>{{ JSON.stringify(healthResult, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>聊天API测试</h3>
      <input 
        v-model="testMessage" 
        placeholder="输入测试消息" 
        @keyup.enter="testChat"
      />
      <button @click="testChat" :disabled="loading || !testMessage.trim()">
        发送消息
      </button>
      <div v-if="chatResult" class="result">
        <pre>{{ JSON.stringify(chatResult, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>错误信息</h3>
      <div v-if="error" class="error">
        {{ error }}
      </div>
    </div>

    <div class="test-section">
      <h3>加载状态</h3>
      <div>{{ loading ? '加载中...' : '空闲' }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { chatAPI } from '@/services/api';

const loading = ref(false);
const healthResult = ref(null);
const chatResult = ref(null);
const error = ref('');
const testMessage = ref('你好，请介绍一下自己');

const testHealth = async () => {
  loading.value = true;
  error.value = '';
  try {
    const response = await fetch('http://localhost:8000/health');
    const data = await response.json();
    healthResult.value = data;
    console.log('健康检查结果:', data);
  } catch (err) {
    error.value = `健康检查失败: ${err}`;
    console.error('健康检查失败:', err);
  } finally {
    loading.value = false;
  }
};

const testChat = async () => {
  if (!testMessage.value.trim()) return;
  
  loading.value = true;
  error.value = '';
  try {
    const response = await chatAPI.sendMessage({
      message: testMessage.value,
      thread_id: `test_${Date.now()}`
    });
    chatResult.value = response;
    console.log('聊天API结果:', response);
  } catch (err) {
    error.value = `聊天API失败: ${err}`;
    console.error('聊天API失败:', err);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.api-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
}

button {
  padding: 8px 16px;
  margin: 8px 8px 8px 0;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

input {
  padding: 8px;
  margin: 8px 8px 8px 0;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 300px;
}

.result {
  margin-top: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.error {
  color: #dc3545;
  background: #f8d7da;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
}

pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
