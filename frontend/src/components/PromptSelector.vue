<template>
  <div class="prompt-selector" :class="{ expanded: isExpanded }">
    <!-- 触发按钮 -->
    <button 
      @click="toggleExpanded"
      class="prompt-trigger"
      :class="{ active: isExpanded }"
      title="快捷提示词"
    >
      <span class="icon">💡</span>
      <span class="text">提示词</span>
      <span class="arrow" :class="{ rotated: isExpanded }">▼</span>
    </button>

    <!-- 提示词面板 -->
    <div v-if="isExpanded" class="prompt-panel">
      <!-- 搜索栏 -->
      <div class="prompt-search">
        <input
          v-model="searchQuery"
          placeholder="搜索提示词..."
          class="search-input"
          @input="handleSearch"
        />
        <button @click="clearSearch" class="clear-btn" v-if="searchQuery">
          ✕
        </button>
      </div>

      <!-- 分类标签 -->
      <div class="category-tabs">
        <button
          v-for="category in categories"
          :key="category.id"
          @click="selectCategory(category.id)"
          class="category-tab"
          :class="{ active: selectedCategory === category.id }"
          :style="{ '--category-color': category.color }"
        >
          <span class="category-icon">{{ category.icon }}</span>
          <span class="category-name">{{ category.name }}</span>
        </button>
      </div>

      <!-- 提示词列表 -->
      <div class="prompt-list">
        <div v-if="filteredPrompts.length === 0" class="empty-state">
          <div class="empty-icon">🔍</div>
          <div class="empty-text">
            {{ searchQuery ? '没有找到匹配的提示词' : '该分类下暂无提示词' }}
          </div>
        </div>

        <div
          v-for="prompt in filteredPrompts"
          :key="prompt.id"
          @click="selectPrompt(prompt)"
          class="prompt-item"
          :class="{ 'has-variables': prompt.variables && prompt.variables.length > 0 }"
        >
          <div class="prompt-header">
            <div class="prompt-icon">{{ prompt.icon || '📝' }}</div>
            <div class="prompt-info">
              <div class="prompt-title">{{ prompt.title }}</div>
              <div class="prompt-description" v-if="prompt.description">
                {{ prompt.description }}
              </div>
            </div>
            <div class="prompt-actions">
              <button
                v-if="prompt.variables && prompt.variables.length > 0"
                @click.stop="openVariableDialog(prompt)"
                class="action-btn variable-btn"
                title="配置变量"
              >
                ⚙️
              </button>
              <button
                v-if="prompt.isCustom"
                @click.stop="editPrompt(prompt)"
                class="action-btn edit-btn"
                title="编辑"
              >
                ✏️
              </button>
              <button
                v-if="prompt.isCustom"
                @click.stop="deletePrompt(prompt.id)"
                class="action-btn delete-btn"
                title="删除"
              >
                🗑️
              </button>
            </div>
          </div>
          
          <div class="prompt-tags" v-if="prompt.tags && prompt.tags.length > 0">
            <span
              v-for="tag in prompt.tags"
              :key="tag"
              class="prompt-tag"
            >
              {{ tag }}
            </span>
          </div>
          
          <div class="prompt-preview">
            {{ getPromptPreview(prompt.content) }}
          </div>
        </div>
      </div>

      <!-- 底部操作 -->
      <div class="prompt-footer">
        <button @click="openCreateDialog" class="create-btn">
          ➕ 创建提示词
        </button>
        <button @click="openManageDialog" class="manage-btn">
          ⚙️ 管理
        </button>
      </div>
    </div>

    <!-- 变量配置对话框 -->
    <VariableDialog
      v-if="showVariableDialog"
      :prompt="selectedPromptForVariables"
      @confirm="handleVariableConfirm"
      @cancel="closeVariableDialog"
    />

    <!-- 创建/编辑提示词对话框 -->
    <PromptEditDialog
      v-if="showEditDialog"
      :prompt="editingPrompt"
      @save="handlePromptSave"
      @cancel="closeEditDialog"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { promptCategories, PromptManager, type PromptItem } from '@/data/prompts';
import VariableDialog from './VariableDialog.vue';
import PromptEditDialog from './PromptEditDialog.vue';

const emit = defineEmits<{
  'prompt-selected': [content: string];
}>();

// 响应式数据
const isExpanded = ref(false);
const searchQuery = ref('');
const selectedCategory = ref('writing');
const showVariableDialog = ref(false);
const showEditDialog = ref(false);
const selectedPromptForVariables = ref<PromptItem | null>(null);
const editingPrompt = ref<PromptItem | null>(null);

// 计算属性
const categories = computed(() => promptCategories);

const allPrompts = computed(() => PromptManager.getAllPrompts());

const filteredPrompts = computed(() => {
  let prompts = allPrompts.value;

  // 按分类过滤
  if (selectedCategory.value !== 'all') {
    prompts = prompts.filter(p => p.category === selectedCategory.value);
  }

  // 按搜索词过滤
  if (searchQuery.value) {
    prompts = PromptManager.searchPrompts(searchQuery.value);
  }

  // 按使用次数排序
  return prompts.sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0));
});

// 方法
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
};

const selectCategory = (categoryId: string) => {
  selectedCategory.value = categoryId;
  searchQuery.value = '';
};

const handleSearch = () => {
  // 搜索时重置分类选择
  if (searchQuery.value) {
    selectedCategory.value = 'all';
  }
};

const clearSearch = () => {
  searchQuery.value = '';
};

const selectPrompt = (prompt: PromptItem) => {
  if (prompt.variables && prompt.variables.length > 0) {
    openVariableDialog(prompt);
  } else {
    emit('prompt-selected', prompt.content);
    PromptManager.incrementUsage(prompt.id);
    isExpanded.value = false;
  }
};

const openVariableDialog = (prompt: PromptItem) => {
  selectedPromptForVariables.value = prompt;
  showVariableDialog.value = true;
};

const closeVariableDialog = () => {
  showVariableDialog.value = false;
  selectedPromptForVariables.value = null;
};

const handleVariableConfirm = (content: string) => {
  emit('prompt-selected', content);
  if (selectedPromptForVariables.value) {
    PromptManager.incrementUsage(selectedPromptForVariables.value.id);
  }
  closeVariableDialog();
  isExpanded.value = false;
};

const openCreateDialog = () => {
  editingPrompt.value = null;
  showEditDialog.value = true;
};

const editPrompt = (prompt: PromptItem) => {
  editingPrompt.value = prompt;
  showEditDialog.value = true;
};

const closeEditDialog = () => {
  showEditDialog.value = false;
  editingPrompt.value = null;
};

const handlePromptSave = (prompt: PromptItem) => {
  // 保存逻辑在对话框组件中处理
  closeEditDialog();
};

const deletePrompt = (promptId: string) => {
  if (confirm('确定要删除这个提示词吗？')) {
    PromptManager.deletePrompt(promptId);
  }
};

const openManageDialog = () => {
  // TODO: 实现提示词管理界面
  console.log('打开提示词管理界面');
};

const getPromptPreview = (content: string): string => {
  // 移除变量占位符，显示预览
  const preview = content.replace(/\{[^}]+\}/g, '...');
  return preview.length > 100 ? preview.slice(0, 100) + '...' : preview;
};

// 点击外部关闭
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as Element;
  if (!target.closest('.prompt-selector')) {
    isExpanded.value = false;
  }
};

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.prompt-selector {
  position: relative;
  display: inline-block;
}

.prompt-trigger {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.prompt-trigger:hover {
  background: #f5f5f5;
  border-color: #1890ff;
}

.prompt-trigger.active {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.prompt-trigger .arrow {
  font-size: 12px;
  transition: transform 0.2s;
}

.prompt-trigger .arrow.rotated {
  transform: rotate(180deg);
}

.prompt-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  min-width: 400px;
  max-width: 600px;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-top: 4px;
}

.prompt-search {
  position: relative;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
}

.search-input:focus {
  border-color: #1890ff;
}

.clear-btn {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #999;
  font-size: 12px;
}

.category-tabs {
  display: flex;
  padding: 8px 12px;
  gap: 4px;
  border-bottom: 1px solid #f0f0f0;
  overflow-x: auto;
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 16px;
  background: white;
  cursor: pointer;
  font-size: 12px;
  white-space: nowrap;
  transition: all 0.2s;
}

.category-tab:hover {
  background: #f5f5f5;
}

.category-tab.active {
  background: var(--category-color, #1890ff);
  color: white;
  border-color: var(--category-color, #1890ff);
}

.prompt-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 8px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.prompt-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.prompt-item:hover {
  border-color: #1890ff;
  background: #f6f9ff;
}

.prompt-item.has-variables {
  border-left: 3px solid #fa8c16;
}

.prompt-header {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
}

.prompt-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.prompt-info {
  flex: 1;
  min-width: 0;
}

.prompt-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.prompt-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.prompt-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.prompt-item:hover .prompt-actions {
  opacity: 1;
}

.action-btn {
  padding: 4px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 3px;
  font-size: 12px;
  transition: background 0.2s;
}

.action-btn:hover {
  background: rgba(0, 0, 0, 0.1);
}

.prompt-tags {
  display: flex;
  gap: 4px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.prompt-tag {
  padding: 2px 6px;
  background: #f0f0f0;
  border-radius: 10px;
  font-size: 10px;
  color: #666;
}

.prompt-preview {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.prompt-footer {
  display: flex;
  gap: 8px;
  padding: 12px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.create-btn,
.manage-btn {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.create-btn {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.create-btn:hover {
  background: #40a9ff;
}

.manage-btn:hover {
  background: #f5f5f5;
  border-color: #1890ff;
}
</style>
