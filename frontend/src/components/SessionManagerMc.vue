<template>
  <div class="session-manager-mc" :class="{ collapsed: isCollapsed }">
    <!-- 头部 -->
    <div class="session-header">
      <div class="header-title" v-if="!isCollapsed">
        <h3>会话管理</h3>
        <button @click="createNewSession" class="new-session-btn" title="新建会话">
          ➕
        </button>
      </div>
      <button @click="toggleCollapse" class="collapse-btn" :title="isCollapsed ? '展开' : '收起'">
        {{ isCollapsed ? '📋' : '◀' }}
      </button>
    </div>

    <!-- 会话列表 -->
    <div v-if="!isCollapsed" class="session-content">
      <!-- 搜索框 -->
      <div class="search-section">
        <input 
          v-model="searchQuery" 
          placeholder="搜索会话..." 
          class="search-input"
        />
      </div>

      <!-- 使用McList显示会话列表 -->
      <McList 
        :data="sessionListData"
        @select="handleSessionSelect"
        class="session-list"
        style="height: calc(100vh - 200px); overflow-y: auto;"
      >
        <template #item="{ item }">
          <div class="session-item-content" :class="{ active: item.value === currentSessionId }">
            <div class="session-info">
              <div class="session-title">{{ item.label }}</div>
              <div class="session-meta">
                <span class="message-count">{{ item.messageCount }} 条消息</span>
                <span class="last-time">{{ formatTime(item.lastActiveTime) }}</span>
              </div>
            </div>
            <div class="session-actions">
              <button @click.stop="startEdit(item.value, item.label)" class="action-btn edit-btn" title="重命名">
                ✏️
              </button>
              <button @click.stop="deleteSession(item.value)" class="action-btn delete-btn" title="删除">
                🗑️
              </button>
            </div>
          </div>
        </template>
      </McList>
    </div>

    <!-- 收起状态的指示器 -->
    <div v-else class="collapsed-indicator">
      <div 
        v-for="session in sessions.slice(0, 5)" 
        :key="session.id"
        class="session-dot"
        :class="{ active: session.id === currentSessionId }"
        @click="selectSession(session.id)"
        :title="session.title || '新对话'"
      ></div>
      <div v-if="sessions.length > 5" class="more-sessions">
        +{{ sessions.length - 5 }}
      </div>
    </div>

    <!-- 编辑对话框 -->
    <div v-if="editingSessionId" class="edit-modal" @click="cancelEdit">
      <div class="edit-dialog" @click.stop>
        <h4>重命名会话</h4>
        <input 
          v-model="editingTitle"
          @keyup.enter="saveSessionTitle"
          @keyup.escape="cancelEdit"
          class="edit-input"
          ref="editInput"
        />
        <div class="edit-actions">
          <button @click="saveSessionTitle" class="save-btn">保存</button>
          <button @click="cancelEdit" class="cancel-btn">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';
import { McList } from '@matechat/core';

interface Session {
  id: string;
  title: string;
  messageCount: number;
  lastActiveTime: Date;
  createdAt: Date;
}

interface Props {
  sessions: Session[];
  currentSessionId: string;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'session-select': [sessionId: string];
  'session-create': [];
  'session-delete': [sessionId: string];
  'session-rename': [sessionId: string, newTitle: string];
  'session-reorder': [sessionIds: string[]];
}>();

// 响应式数据
const isCollapsed = ref(false);
const searchQuery = ref('');
const editingSessionId = ref<string | null>(null);
const editingTitle = ref('');
const editInput = ref<HTMLInputElement>();

// 计算属性
const filteredSessions = computed(() => {
  if (!searchQuery.value) return props.sessions;
  return props.sessions.filter(session => 
    session.title.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

// 转换为McList需要的数据格式
const sessionListData = computed(() => {
  return filteredSessions.value.map(session => ({
    label: session.title || '新对话',
    value: session.id,
    messageCount: session.messageCount,
    lastActiveTime: session.lastActiveTime,
    active: session.id === props.currentSessionId
  }));
});

// 方法
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

const createNewSession = () => {
  emit('session-create');
};

const handleSessionSelect = (item: any) => {
  selectSession(item.value);
};

const selectSession = (sessionId: string) => {
  if (editingSessionId.value) return;
  emit('session-select', sessionId);
};

const deleteSession = (sessionId: string) => {
  if (confirm('确定要删除这个会话吗？删除后无法恢复。')) {
    emit('session-delete', sessionId);
  }
};

const startEdit = async (sessionId: string, currentTitle: string) => {
  editingSessionId.value = sessionId;
  editingTitle.value = currentTitle || '新对话';
  
  await nextTick();
  if (editInput.value) {
    editInput.value.focus();
    editInput.value.select();
  }
};

const saveSessionTitle = () => {
  if (editingSessionId.value && editingTitle.value.trim()) {
    emit('session-rename', editingSessionId.value, editingTitle.value.trim());
  }
  cancelEdit();
};

const cancelEdit = () => {
  editingSessionId.value = null;
  editingTitle.value = '';
};

const formatTime = (date: Date): string => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;
  return date.toLocaleDateString('zh-CN');
};
</script>

<style scoped>
.session-manager-mc {
  width: 300px;
  background: white;
  border-right: 1px solid #e5e5e5;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
}

.session-manager-mc.collapsed {
  width: 60px;
}

.session-header {
  padding: 16px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-title h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.new-session-btn, .collapse-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 14px;
}

.new-session-btn:hover, .collapse-btn:hover {
  background: #f0f0f0;
}

.session-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.search-section {
  padding: 16px;
  border-bottom: 1px solid #e5e5e5;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.session-list {
  flex: 1;
}

.session-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  border-radius: 6px;
  margin: 4px 8px;
}

.session-item-content:hover {
  background: #f8f9fa;
}

.session-item-content.active {
  background: #e3f2fd;
  border-left: 3px solid #2196f3;
}

.session-info {
  flex: 1;
  min-width: 0;
}

.session-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.session-meta {
  font-size: 12px;
  color: #666;
  display: flex;
  gap: 8px;
}

.session-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.session-item-content:hover .session-actions {
  opacity: 1;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 12px;
}

.action-btn:hover {
  background: #e0e0e0;
}

.collapsed-indicator {
  padding: 16px 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.session-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ddd;
  cursor: pointer;
}

.session-dot.active {
  background: #2196f3;
}

.more-sessions {
  font-size: 10px;
  color: #666;
  text-align: center;
}

.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.edit-dialog {
  background: white;
  padding: 24px;
  border-radius: 8px;
  min-width: 300px;
}

.edit-dialog h4 {
  margin: 0 0 16px 0;
}

.edit-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 16px;
}

.edit-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.save-btn, .cancel-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.save-btn {
  background: #2196f3;
  color: white;
}

.cancel-btn {
  background: #f0f0f0;
  color: #333;
}
</style>
