<template>
  <div class="chat-container">
    <!-- 会话管理侧边栏 -->
    <SessionManager
      :sessions="sessions"
      :current-session-id="currentSessionId"
      @session-select="handleSessionSelect"
      @session-create="handleSessionCreate"
      @session-delete="handleSessionDelete"
      @session-rename="handleSessionRename"
      @session-reorder="handleSessionReorder"
    />

    <!-- 主聊天区域 -->
    <div class="chat-main">
    <!-- 头部 -->
    <div class="chat-header">
      <div class="header-content">
        <div class="logo-section">
          <img src="https://matechat.gitcode.com/logo.svg" alt="AI助手" class="logo" />
          <h1 class="title">AI智能助手</h1>
        </div>
        <div class="operations">
          <label class="stream-toggle">
            <input
              type="checkbox"
              v-model="useStreaming"
              class="stream-checkbox"
            />
            <span class="stream-label">流式响应</span>
          </label>
          <button @click="clearChat" class="clear-btn">
            <span class="icon">🗑️</span>
            清空对话
          </button>
          <div class="connection-status" :class="{ connected: isConnected }">
            {{ isConnected ? '已连接' : '未连接' }}
          </div>
        </div>
      </div>
    </div>

    <!-- 欢迎页面 -->
    <div v-if="!hasMessages" class="welcome-container">
      <div class="welcome-content">
        <img src="https://matechat.gitcode.com/logo2x.svg" alt="AI助手" class="welcome-logo" />
        <h2 class="welcome-title">AI智能助手</h2>
        <p class="welcome-subtitle">Hi，欢迎使用智能助手</p>
        <div class="welcome-description">
          <p>我是您的AI智能助手，可以帮助您：</p>
          <ul>
            <li>• 回答各种问题</li>
            <li>• 搜索网络信息</li>
            <li>• 生成图表和可视化</li>
            <li>• 进行逻辑推理和分析</li>
          </ul>
        </div>
        <div class="quick-prompts">
          <button 
            v-for="prompt in quickPrompts" 
            :key="prompt.label"
            @click="handleQuickPrompt(prompt)"
            class="prompt-btn"
          >
            {{ prompt.label }}
          </button>
        </div>
      </div>
    </div>

    <!-- 聊天内容 -->
    <div v-else class="messages-container">
      <div class="messages-list" ref="messagesContainer">
        <div 
          v-for="(message, index) in messages" 
          :key="index"
          class="message-item"
          :class="{ 'user-message': message.role === 'user', 'ai-message': message.role === 'assistant' }"
        >
          <div class="message-avatar">
            <img 
              :src="message.role === 'user' ? 'https://matechat.gitcode.com/png/demo/userAvatar.svg' : 'https://matechat.gitcode.com/logo.svg'" 
              :alt="message.role === 'user' ? '用户' : 'AI助手'"
            />
          </div>
          <div class="message-content">
            <div class="message-text">
              <!-- AI消息使用MateChat的MarkdownCard -->
              <McMarkdownCard
                v-if="message.role === 'assistant'"
                :content="message.content"
                :typing="shouldUseTypewriter(message, index) && useStreaming"
                :typing-options="{
                  step: 2,
                  interval: 30,
                  style: 'cursor'
                }"
                :theme="'light'"
                :md-options="{
                  breaks: true,
                  linkify: true,
                  html: false,
                  typographer: true,
                  quotes: '""''',
                  xhtmlOut: false
                }"
                @typing-end="onTypewriterComplete(index)"
              />
              <!-- 用户消息使用普通文本 -->
              <span v-else class="user-message">{{ message.content }}</span>
            </div>
            <div class="message-time">{{ formatTimestamp(message.timestamp) }}</div>
          </div>
        </div>
        
        <!-- 加载指示器 -->
        <div v-if="isLoading" class="message-item ai-message loading-message">
          <div class="message-avatar">
            <img src="https://matechat.gitcode.com/logo.svg" alt="AI助手" />
          </div>
          <div class="message-content">
            <div class="message-text">正在思考中...</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-container">
      <!-- 文件上传区域 -->
      <div v-if="showFileUpload" class="file-upload-area">
        <FileUpload
          @file-uploaded="handleFileUploaded"
          @file-removed="handleFileRemoved"
        />
      </div>

      <div class="input-wrapper">
        <div class="input-controls">
          <PromptSelector @prompt-selected="handlePromptSelected" />
          <button
            @click="toggleFileUpload"
            class="control-btn file-btn"
            :class="{ active: showFileUpload }"
            title="文件上传"
          >
            <span class="icon">📎</span>
          </button>
        </div>

        <input
          v-model="inputMessage"
          :placeholder="'输入您的问题...'"
          :disabled="isLoading"
          @keyup.enter="handleSendMessage"
          class="message-input"
        />

        <button
          @click="handleSendMessage"
          :disabled="!inputMessage.trim() || isLoading"
          class="send-button"
        >
          <span class="icon">📤</span>
        </button>
      </div>
    </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { useChatStore } from '@/stores/chat';
import TypewriterText from './TypewriterText.vue';
import SessionManager from './SessionManager.vue';
import FileUpload from './FileUpload.vue';
import PromptSelector from './PromptSelector.vue';
import { McMarkdownCard } from '@matechat/core';
import type { FileUploadResponse } from '@/services/api';

const chatStore = useChatStore();
const inputMessage = ref('');
const messagesContainer = ref<HTMLElement>();
const typewriterStates = ref<Set<number>>(new Set()); // 跟踪哪些消息正在使用打字机效果
const useStreaming = ref(true); // 是否使用流式响应
const showFileUpload = ref(false); // 是否显示文件上传区域
const uploadedFiles = ref<FileUploadResponse[]>([]); // 已上传的文件

// 计算属性
const messages = computed(() => chatStore.messages);
const hasMessages = computed(() => chatStore.hasMessages);
const isLoading = computed(() => chatStore.isLoading);
const isConnected = computed(() => chatStore.isConnected);
const sessions = computed(() => chatStore.sessions);
const currentSessionId = computed(() => chatStore.currentSessionId);

// 快捷提示
const quickPrompts = ref([
  { label: '你好，请介绍一下自己' },
  { label: '帮我搜索最新的AI发展趋势' },
  { label: '生成一个数据可视化图表' },
  { label: '帮我分析一个复杂问题' },
]);

// 方法
const formatTimestamp = (timestamp?: string) => {
  if (!timestamp) return '';
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
  });
};

const handleSendMessage = async () => {
  if (!inputMessage.value.trim() || isLoading.value) return;

  const message = inputMessage.value;
  inputMessage.value = '';

  await chatStore.sendMessage(message, useStreaming.value);
  await scrollToBottom();
};

const handleQuickPrompt = (prompt: { label: string }) => {
  inputMessage.value = prompt.label;
  handleSendMessage();
};

const clearChat = () => {
  chatStore.clearMessages();
};

const scrollToBottom = async () => {
  await nextTick();
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

// 判断是否应该使用打字机效果
const shouldUseTypewriter = (message: any, index: number) => {
  // 只对最新的AI消息使用打字机效果
  return message.role === 'assistant' &&
         index === messages.value.length - 1 &&
         isLoading.value;
};

// 打字机效果完成回调
const onTypewriterComplete = (index: number) => {
  typewriterStates.value.delete(index);
};

// 监听消息变化，自动滚动到底部
watch(messages, () => {
  scrollToBottom();
}, { deep: true });

// 会话管理方法
const handleSessionSelect = (sessionId: string) => {
  chatStore.switchToSession(sessionId);
};

const handleSessionCreate = () => {
  chatStore.createNewSession();
};

const handleSessionDelete = (sessionId: string) => {
  chatStore.deleteSession(sessionId);
};

const handleSessionRename = (sessionId: string, newTitle: string) => {
  chatStore.renameSession(sessionId, newTitle);
};

const handleSessionReorder = (sessionIds: string[]) => {
  chatStore.reorderSessions(sessionIds);
};

// 文件上传相关方法
const toggleFileUpload = () => {
  showFileUpload.value = !showFileUpload.value;
};

const handleFileUploaded = (file: FileUploadResponse) => {
  uploadedFiles.value.push(file);
  console.log('文件上传成功:', file);

  // 可以在这里添加文件到消息中
  const fileMessage = `📎 已上传文件: ${file.filename} (${formatFileSize(file.file_size)})`;
  // 这里可以选择是否自动发送文件信息
};

const handleFileRemoved = (fileId: string) => {
  uploadedFiles.value = uploadedFiles.value.filter(f => f.file_id !== fileId);
  console.log('文件已删除:', fileId);
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 提示词相关方法
const handlePromptSelected = (content: string) => {
  inputMessage.value = content;
  // 可以选择是否自动发送
  // handleSendMessage();
};

// 生命周期
onMounted(() => {
  chatStore.initializeStore();
  chatStore.connectWebSocket();
});

onUnmounted(() => {
  chatStore.disconnectWebSocket();
});
</script>

<style scoped>
.chat-container {
  height: 100vh;
  display: flex;
  background: #f5f5f5;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.chat-header {
  background: white;
  border-bottom: 1px solid #eee;
  padding: 16px 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  width: 32px;
  height: 32px;
}

.title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.operations {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stream-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
}

.stream-checkbox {
  margin: 0;
}

.stream-label {
  user-select: none;
}

.clear-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  transition: all 0.2s;
}

.clear-btn:hover {
  background: #f5f5f5;
  border-color: #ccc;
}

.connection-status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  background: #f0f0f0;
  color: #666;
}

.connection-status.connected {
  background: #e6f7ff;
  color: #1890ff;
}

.welcome-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.welcome-content {
  text-align: center;
  max-width: 600px;
}

.welcome-logo {
  width: 80px;
  height: 80px;
  margin-bottom: 24px;
}

.welcome-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.welcome-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0 0 24px 0;
}

.welcome-description {
  text-align: left;
  margin-bottom: 32px;
}

.welcome-description p {
  margin: 0 0 12px 0;
  color: #333;
}

.welcome-description ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.welcome-description li {
  padding: 4px 0;
  color: #666;
}

.quick-prompts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.prompt-btn {
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  text-align: left;
  font-size: 14px;
  transition: all 0.2s;
}

.prompt-btn:hover {
  border-color: #1890ff;
  background: #f6f9ff;
}

.messages-container {
  flex: 1;
  overflow: hidden;
  background: white;
  margin: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.messages-list {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-item {
  display: flex;
  gap: 12px;
  max-width: 80%;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.ai-message {
  align-self: flex-start;
}

.message-avatar {
  flex-shrink: 0;
}

.message-avatar img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
}

.message-content {
  flex: 1;
}

.message-text {
  word-wrap: break-word;
}

.user-message {
  display: block;
  padding: 12px 16px;
  background: #1890ff;
  color: white;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.5;
}

/* MateChat MarkdownCard 样式优化 */
.message.ai-message :deep(.mc-markdown-render) {
  background: transparent;
  padding: 12px 16px;
  border-radius: 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  font-size: 14px;
  line-height: 1.6;
}

.message.ai-message :deep(.mc-markdown-render p) {
  margin: 0.5em 0;
}

.message.ai-message :deep(.mc-markdown-render p:first-child) {
  margin-top: 0;
}

.message.ai-message :deep(.mc-markdown-render p:last-child) {
  margin-bottom: 0;
}

.message.ai-message :deep(.mc-markdown-render code) {
  background: #e9ecef;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.9em;
}

.message.ai-message :deep(.mc-markdown-render pre) {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  overflow-x: auto;
  margin: 0.5em 0;
}

.message.ai-message :deep(.mc-markdown-render blockquote) {
  border-left: 4px solid #1890ff;
  padding-left: 12px;
  margin: 0.5em 0;
  color: #666;
  font-style: italic;
}

.message.ai-message :deep(.mc-markdown-render ul),
.message.ai-message :deep(.mc-markdown-render ol) {
  padding-left: 1.5em;
  margin: 0.5em 0;
}

.message.ai-message :deep(.mc-markdown-render table) {
  border-collapse: collapse;
  width: 100%;
  margin: 0.5em 0;
}

.message.ai-message :deep(.mc-markdown-render th),
.message.ai-message :deep(.mc-markdown-render td) {
  border: 1px solid #e9ecef;
  padding: 8px 12px;
  text-align: left;
}

.message.ai-message :deep(.mc-markdown-render th) {
  background: #f8f9fa;
  font-weight: 600;
}

.message.ai-message :deep(.mc-markdown-render a) {
  color: #1890ff;
  text-decoration: none;
}

.message.ai-message :deep(.mc-markdown-render a:hover) {
  text-decoration: underline;
}
}

.user-message .message-text {
  background: #1890ff;
  color: white;
  border-bottom-right-radius: 4px;
}

.ai-message .message-text {
  background: #f5f5f5;
  color: #333;
  border-bottom-left-radius: 4px;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  text-align: right;
}

.user-message .message-time {
  text-align: left;
}

.loading-message {
  opacity: 0.7;
}

.loading-message .message-text {
  background: #f0f0f0;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

.input-container {
  background: white;
  border-top: 1px solid #eee;
}

.file-upload-area {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 800px;
  margin: 0 auto;
  padding: 16px 24px;
}

.input-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  font-size: 16px;
}

.control-btn:hover {
  background: #f5f5f5;
  border-color: #ccc;
}

.control-btn.active {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.message-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 24px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.message-input:focus {
  border-color: #1890ff;
}

.message-input:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.send-button {
  padding: 12px 16px;
  border: none;
  background: #1890ff;
  color: white;
  border-radius: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  min-width: 48px;
}

.send-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.send-button:not(:disabled):hover {
  background: #40a9ff;
}

.icon {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-header {
    padding: 12px 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .messages-container {
    margin: 8px;
  }

  .message-item {
    max-width: 90%;
  }

  .input-container {
    padding: 12px 16px;
  }

  .quick-prompts {
    grid-template-columns: 1fr;
  }
}
</style>
